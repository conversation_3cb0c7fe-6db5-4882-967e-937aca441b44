from django.test import TestCase

from factories.integration import IntegrationFactory


class TestIntegration(TestCase):
    def test_str(self):
        integration = IntegrationFactory()
        technology_id = (
            integration.technology.technology_id
            if integration.technology
            else "unknown"
        )
        self.assertEqual(
            f"{integration.id} [{technology_id} ({integration.organization.alias})]",
            str(integration),
        )
