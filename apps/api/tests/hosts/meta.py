from apps.accounts.models import Account, Organization
from apps.assets.tests.es_base import ESCaseMixin
from apps.integrations.models import Integration, Technology
from apps.tests.base import ApiDatabaseTestCase
from factories.integration import IntegrationFactory
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import MergedSourceHostFactory


class HostMetaTestCase(ESCaseMixin, ApiDatabaseTestCase):
    fixtures = ["accounts.json"]

    def get_meta(self):
        response = self.api_client.get("/api/v1/hosts/meta")
        self.assertEqual(200, response.status_code, response.json())
        return response

    def test_host_meta_no_technologies(self):
        response = self.get_meta()
        meta = response.json()
        self.assertIn("coverage_gap", meta)
        self.assertIn("os_family", meta)
        self.assertIn("criticality", meta)
        self.assertIn("technology", meta)
        self.assertEqual(0, len(meta["technology"]))

    def test_host_meta_with_technologies(self):
        account, _ = Account.objects.get_or_create(alias="different_account")
        different_org, _ = Organization.objects.get_or_create(
            alias="different_org", account=account
        )
        tenable_tech, _ = Technology.objects.get_or_create(
            technology_id="tenable_io", defaults={"name": "Tenable IO"}
        )
        sentinel_tech, _ = Technology.objects.get_or_create(
            technology_id="sentinel_one", defaults={"name": "SentinelOne"}
        )
        carbon_tech, _ = Technology.objects.get_or_create(
            technology_id="carbon_black", defaults={"name": "Carbon Black"}
        )
        Integration.objects.get_or_create(
            technology=tenable_tech,
            organization=self.organization,
            defaults={"category_id": "vulnerability_management"},
        )
        Integration.objects.get_or_create(
            technology=sentinel_tech,
            organization=self.organization,
            defaults={"category_id": "endpoint_security"},
        )
        Integration.objects.get_or_create(
            technology=carbon_tech,
            organization=different_org,
            defaults={"category_id": "endpoint_security"},
        )

        response = self.get_meta()
        meta = response.json()
        self.assertIn("technology", meta)
        self.assertEqual(2, len(meta["technology"]))

        self.assertTrue(
            set(m["id"] for m in meta["technology"]).issubset(
                set(m["id"] for m in meta["coverage_gap"])
            ),
            "coverage gap meta should include all technologies",
        )

    def test_host_meta_with_technologies_child_org(self):
        account, _ = Account.objects.get_or_create(alias="different_account")
        different_org, _ = Organization.objects.get_or_create(
            alias="different_org", account=account
        )
        tenable_tech, _ = Technology.objects.get_or_create(
            technology_id="tenable_io", defaults={"name": "Tenable IO"}
        )
        carbon_tech, _ = Technology.objects.get_or_create(
            technology_id="carbon_black", defaults={"name": "Carbon Black"}
        )
        sentinel_tech, _ = Technology.objects.get_or_create(
            technology_id="sentinel_one", defaults={"name": "SentinelOne"}
        )
        Integration.objects.get_or_create(
            technology=tenable_tech,
            organization=self.organization,
            defaults={"category_id": "vulnerability_management"},
        )
        Integration.objects.get_or_create(
            technology=carbon_tech,
            organization=self.child_organization,
            defaults={"category_id": "endpoint_security"},
        )
        Integration.objects.get_or_create(
            technology=sentinel_tech,
            organization=different_org,
            defaults={"category_id": "endpoint_security"},
        )

        response = self.get_meta()
        meta = response.json()
        self.assertIn("technology", meta)
        self.assertEqual(2, len(meta["technology"]))

    def test_host_meta_endpoint_security_combined(self):
        IntegrationFactory.create(
            technology_id="defender_atp",
            organization=self.organization,
            category_id="endpoint_security",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            technology_id="carbon_black",
            organization=self.organization,
            category_id="endpoint_security",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )

        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="defender_atp",
                    metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.IGNORE,
                    metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
                MergedSourceHostFactory(
                    metadata__integration__technology_id="carbon_black",
                    metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.IGNORE,
                    metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
            ],
        )

        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="defender_atp",
                    metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.IGNORE,
                    metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                )
            ],
        )
        response = self.get_meta()
        meta = response.json()
        self.assertIn("endpoint_security_combined", meta)
        self.assertListEqual(
            [
                {
                    "id": "carbon_black,defender_atp",
                    "name": "Carbon Black/Microsoft Defender for Endpoint",
                },
                {
                    "id": "defender_atp",
                    "name": "Microsoft Defender for Endpoint",
                },
            ],
            meta["endpoint_security_combined"],
        )

    def test_host_meta_endpoint_security_combined_empty(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["defender_atp", "carbon_black"],
        )

        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["defender_atp"],
        )
        response = self.get_meta()
        meta = response.json()
        self.assertIn("endpoint_security_combined", meta)
        self.assertListEqual(
            [],
            meta["endpoint_security_combined"],
        )

    def test_host_meta_vulnerability_management_combined(self):
        IntegrationFactory.create(
            technology_id="qualys_vmpc",
            organization=self.organization,
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            technology_id="tenable_io",
            organization=self.organization,
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )

        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="qualys_vmpc",
                    metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
                MergedSourceHostFactory(
                    metadata__integration__technology_id="tenable_io",
                    metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
            ],
        )

        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="qualys_vmpc",
                    metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
            ],
        )
        response = self.get_meta()
        meta = response.json()
        self.assertIn("vulnerability_management_combined", meta)
        self.assertListEqual(
            [
                {
                    "id": "qualys_vmpc",
                    "name": "Qualys VMDR",
                },
                {
                    "id": "qualys_vmpc,tenable_io",
                    "name": "Qualys VMDR/Tenable IO",
                },
            ],
            meta["vulnerability_management_combined"],
        )

    def test_host_meta_vulnerability_management_combined_empty(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="qualys_vmpc",
                    metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
                MergedSourceHostFactory(
                    metadata__integration__technology_id="tenable_io",
                    metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
            ],
        )

        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="qualys_vmpc",
                    metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
            ],
        )
        response = self.get_meta()
        meta = response.json()
        self.assertIn("vulnerability_management_combined", meta)
        self.assertListEqual(
            [],
            meta["vulnerability_management_combined"],
        )

    def test_host_meta_coverage_gaps_no_vulnerability_management(self):
        IntegrationFactory.create(
            technology_id="sentinel_one",
            organization=self.organization,
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )

        response = self.get_meta()
        meta = response.json()
        self.assertIn("coverage_gap", meta)
        self.assertEqual(4, len(meta["coverage_gap"]))
        self.assertNotIn("Vulnerability", meta["coverage_gap"])
        self.assertNotIn("Technical Security Control", meta["coverage_gap"])

    def test_host_meta_coverage_gaps_no_endpoint_security(self):
        IntegrationFactory.create(
            technology_id="qualys_vmpc",
            organization=self.organization,
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )

        response = self.get_meta()
        meta = response.json()
        self.assertIn("coverage_gap", meta)
        self.assertEqual(4, len(meta["coverage_gap"]))
        self.assertNotIn("Endpoint Security", meta["coverage_gap"])
        self.assertNotIn("Technical Security Control", meta["coverage_gap"])
