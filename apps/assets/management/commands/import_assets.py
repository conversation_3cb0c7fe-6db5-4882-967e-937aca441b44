import json
import logging
from argparse import <PERSON><PERSON><PERSON>tHelp<PERSON>ormatter
from io import BytesIO
from typing import Generator
from uuid import UUID, uuid5

import boto3
from django.core.management.base import BaseCommand

from apps.accounts.models import Account, Organization
from apps.assets.models import MergedAsset
from apps.assets.services import merge_service
from apps.integrations.models import Integration
from apps.integrations.utils import get_technology_category

logger = logging.getLogger(__name__)

logging.getLogger("opensearch").disabled = True

TEST_ID = UUID(int=0)


class Command(BaseCommand):
    """
    Management command to import staged assets from a file.
    """

    def create_parser(self, *args, **kwargs):
        parser = super(Command, self).create_parser(*args, **kwargs)
        parser.formatter_class = RawTextHelpFormatter
        return parser

    help = """
    Import staged assets from a file

    There are three ways to specify the integration:
    - integration_id: UUID of the integration
    - integration_query: Query of the form {organization_alias}:{technology_id}
    - technology_id: Technology ID (used to create a test integration automatically)

    There are two ways to specify the filename:
    - filename: Local filename
    - s3://bucket/key: S3 URI

    Example:
        ./manage.py import_assets --technology_id defender_atp --filename s3://bucket/key
        ./manage.py import_assets --integration_id 00000000-0000-0000-0000-000000000000 --filename /path/to/raw_assets.json
        ./manage.py import_assets --integration_query apt66:defender_atp --filename /path/to/raw_assets.json

    """

    def add_arguments(self, parser):
        integration = parser.add_mutually_exclusive_group(required=True)
        integration.add_argument(
            "--integration_id",
            dest="integration_id",
            type=str,
            help="Integration ID (UUID)",
        )
        integration.add_argument(
            "--integration_query",
            dest="integration_query",
            type=str,
            help="Integration Query ({organization_alias}:{technology_id})",
        )
        integration.add_argument(
            "--technology_id",
            dest="technology_id",
            type=str,
            help="Technology ID (used to create a test integration)",
        )
        parser.add_argument(
            "--type",
            dest="staged_type",
            type=str,
            help="Staged type",
            default="Host",
        )
        parser.add_argument(
            "--filename",
            dest="filename",
            required=True,
            help="Filename of staged assets to import",
        )
        parser.add_argument(
            "--delete_existing",
            dest="delete_existing",
            action="store_true",
            help="Delete existing assets before importing (Testing Org only)",
        )

    def handle(self, *args, **options):
        integration_id = options.get("integration_id")
        integration_query = options.get("integration_query")
        technology_id = options.get("technology_id")
        staged_type = options["staged_type"]
        delete_existing = options.get("delete_existing")

        filename = options["filename"]

        # Read staged assets, normalize them, and update them in ES.
        integration = self.get_integration(
            integration_id, integration_query, technology_id, delete_existing
        )
        staged_assets = list(self.read_staged_assets(filename))

        # TODO: Fix hardcoded asset type and sync type
        merge_service.update_source_assets(integration, staged_assets, staged_type)
        merged_asset_ids = merge_service.update_merged_asset_ids(
            integration.organization, staged_type
        )
        merge_service.reconcile_merged_assets(
            integration.organization.id, merged_asset_ids
        )

    def get_integration(
        self,
        integration_id: str,
        integration_query: str,
        technology_id: str,
        delete_existing: bool,
    ):
        if integration_id:
            integration = Integration.objects.get(id=integration_id)
        elif integration_query:
            alias, technology_id = self.validate_query(integration_query)
            integration = Integration.objects.get(
                technology__technology_id=technology_id, organization__alias=alias
            )
        elif technology_id:
            account, _ = Account.objects.get_or_create(
                id=TEST_ID, defaults={"alias": "test"}
            )
            organization, _ = Organization.objects.get_or_create(
                id=TEST_ID, defaults={"alias": "test", "account": account}
            )
            # Ensure Technology exists first
            from apps.integrations.models import Technology
            from apps.integrations.utils import get_technology_name

            technology, _ = Technology.objects.get_or_create(
                technology_id=technology_id,
                defaults={
                    "name": get_technology_name(technology_id),
                },
            )
            integration, created = Integration.objects.get_or_create(
                id=uuid5(TEST_ID, technology_id),  # Use a deterministic UUID
                defaults={
                    "technology": technology,
                    "category_id": get_technology_category(technology_id),
                    "organization": organization,
                },
            )
            if created:
                msg_prefix = "Created a test organization with alias"
            else:
                msg_prefix = "Using test organization with alias"

            logger.info(f"{msg_prefix} {integration.id} for testing purposes")

            if delete_existing:
                body = {"query": {"term": {"metadata.organization_id": str(TEST_ID)}}}
                assets = list(MergedAsset.documents.scan(body))
                MergedAsset.documents.delete_bulk(assets)
        else:  # pragma: no cover
            raise ValueError(
                "Must provide either integration_id, integration_query, or technology_id"
            )

        return integration

    @staticmethod
    def validate_query(integration_query):
        parts = integration_query.split(":")
        if len(parts) != 2:
            raise ValueError(
                "Integration query must be in the form {organization_alias}:{technology_id}"
            )
        return parts

    @staticmethod
    def read_staged_assets(filename: str) -> Generator[dict, None, None]:
        if filename.startswith("s3://"):
            bucket, key = filename.split("/", 3)[2:]
            client = boto3.client("s3")
            buffer = BytesIO()
            client.download_fileobj(bucket, key, buffer)
            buffer.seek(0)
            data = buffer.readlines()
        else:
            with open(filename, "r") as json_file:
                data = list(json_file)

        for json_item in data:
            json_object = json.loads(json_item)
            yield json_object
