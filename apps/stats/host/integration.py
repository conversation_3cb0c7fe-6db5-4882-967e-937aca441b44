from apps.assets.search.host import HostClient, InventoryFilter
from apps.integrations.models import Integration
from apps.stats.base import BaseStat, StatCategory


class IntegrationStat(BaseStat):
    id = "integration"
    label = "Integrations"
    description = "Hosts by Integrations"
    category = StatCategory.HOST

    def get_data(self, organization_ids, filters: InventoryFilter):
        client = HostClient(organization_ids, filters)
        filters = self.integration_filters(organization_ids)

        body = {
            "size": 0,
            "query": {
                "bool": {
                    "filter": [
                        {"terms": {"metadata.organization_id": organization_ids}}
                    ]
                }
            },
            "aggs": {"integration": {"filters": {"filters": filters}}},
        }

        response = client.search(body)
        if aggregations := response.get("aggregations"):
            totals = {
                id: doc["doc_count"]
                for id, doc in aggregations["integration"]["buckets"].items()
            }
        else:
            totals = {}
        return totals

    @staticmethod
    def integration_filters(organization_ids):
        filters = {}
        for integration in Integration.objects.filter(
            organization_id__in=organization_ids
        ).select_related("technology"):
            integration_id = str(integration.id)
            technology_id = (
                integration.technology.technology_id
                if integration.technology
                else "unknown"
            )
            filters[integration_id] = {
                "term": {
                    f"source_data.{technology_id}.metadata.integration.id": integration_id
                }
            }
        return filters
